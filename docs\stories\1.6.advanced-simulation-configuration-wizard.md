# Story 1.6: 高级模拟配置向导

## Status

Draft

## Story

**As a** 研究人员，
**I want** 拥有完整的6步模拟配置向导来设置复杂的结直肠癌筛查模拟参数，
**so that** 能够通过专业的渐进式披露界面配置疾病建模、筛查策略、经济参数和校准设置。

## Acceptance Criteria

1. 实现6步配置向导框架（QStackedWidget结构，步骤进度指示器）
2. 创建疾病建模配置界面（双通路设置、风险因素权重滑块）
3. 实现筛查策略配置界面（工具配置、时间线设计）
4. 添加经济参数配置界面（成本设置、经济指标）
5. 创建校准设置配置界面（基准值、机器学习配置）
6. 实现模拟执行确认界面（参数确认、启动模拟）
7. 添加实时参数验证和智能帮助系统
8. 实现数据导入集成（拖拽区域，支持CSV、Excel、JSON、YAML）
9. 创建配置预览面板和参数摘要显示
10. 确保符合ai-frontend-prompts.md的专业科学研究界面标准

## Tasks / Subtasks

- [ ] 任务1：实现6步配置向导框架 (AC: 1)

  - [ ] 创建src/interfaces/desktop/windows/advanced_config_wizard.py
  - [ ] 实现AdvancedConfigWizard类，继承QDialog
  - [ ] 创建QStackedWidget管理6个配置步骤页面
  - [ ] 实现步骤进度指示器（QProgressBar，带完成状态标识）
  - [ ] 添加导航按钮栏（上一步、下一步、保存草稿、取消、完成）
  - [ ] 实现步骤间数据传递和状态管理
- [ ] 任务2：创建疾病建模配置界面 (AC: 2)

  - [ ] 创建src/interfaces/desktop/widgets/disease_modeling_widget.py
  - [ ] 实现DiseaseModelingWidget疾病建模配置面板
  - [ ] 添加双通路设置（腺瘤-癌变85%，锯齿状腺瘤15%）
  - [ ] 创建6个风险因素权重滑块（家族史、IBD、肥胖、糖尿病、吸烟、久坐）
  - [ ] 实现0-3倍权重范围，基于文献的默认值
  - [ ] 添加实时影响预览图表（QCustomPlot小图表）
  - [ ] 实现风险因素配置的保存和加载
- [ ] 任务3：实现筛查策略配置界面 (AC: 3)

  - [ ] 创建src/interfaces/desktop/widgets/screening_strategy_widget.py
  - [ ] 实现ScreeningStrategyWidget筛查策略配置面板
  - [ ] 创建筛查工具配置（FIT、结肠镜、乙状结肠镜等）
  - [ ] 实现筛查策略时间线（50-80岁年龄轴）
  - [ ] 添加筛查工具图标和间隔设置（QSpinBox年份输入）
  - [ ] 实现重叠检测和冲突警告系统
  - [ ] 创建筛查策略预览和摘要显示
- [ ] 任务4：添加经济参数配置界面 (AC: 4)

  - [ ] 创建src/interfaces/desktop/widgets/economic_parameters_widget.py
  - [ ] 实现EconomicParametersWidget经济参数配置面板
  - [ ] 添加筛查成本设置（FIT、结肠镜、活检等）
  - [ ] 创建治疗成本配置（手术、化疗、放疗）
  - [ ] 实现QALY权重和效用值设置
  - [ ] 添加经济指标配置（贴现率、时间范围）
  - [ ] 实现成本效益分析参数预览
- [ ] 任务5：创建校准设置配置界面 (AC: 5)

  - [ ] 创建src/interfaces/desktop/widgets/calibration_settings_widget.py
  - [ ] 实现CalibrationSettingsWidget校准设置配置面板
  - [ ] 添加基准值配置（腺瘤患病率、癌症发病率、死亡率）
  - [ ] 创建机器学习参数设置（网络架构、学习率、批次大小）
  - [ ] 实现校准目标权重设置（多个QSlider）
  - [ ] 添加拉丁超立方抽样参数配置
  - [ ] 创建校准策略选择和预估时间显示
- [ ] 任务6：实现模拟执行确认界面 (AC: 6)

  - [ ] 创建src/interfaces/desktop/widgets/simulation_execution_widget.py
  - [ ] 实现SimulationExecutionWidget模拟执行确认面板
  - [ ] 创建完整的参数摘要显示（QGroupBox分组）
  - [ ] 实现配置完整性检查（QProgressBar）
  - [ ] 添加预估运行时间和资源需求显示
  - [ ] 创建关键假设和限制说明
  - [ ] 实现模拟启动按钮和状态管理
- [ ] 任务7：添加实时参数验证和智能帮助系统 (AC: 7)

  - [ ] 扩展src/interfaces/desktop/utils/validators.py
  - [ ] 实现实时参数验证（QLineEdit样式表标示错误字段）
  - [ ] 添加参数冲突检测和警告（QMessageBox）
  - [ ] 创建智能帮助系统（QToolButton触发上下文帮助）
  - [ ] 实现医学文献参考显示（QTextBrowser）
  - [ ] 添加默认值建议和合理范围提示
  - [ ] 创建参数影响说明和实时反馈
- [ ] 任务8：实现数据导入集成 (AC: 8)

  - [ ] 创建src/interfaces/desktop/widgets/data_import_widget.py
  - [ ] 实现DataImportWidget数据导入组件
  - [ ] 创建拖拽区域（QFrame，接受拖拽事件）
  - [ ] 添加CSV、Excel、JSON格式支持
  - [ ] 实现文件格式验证和错误提示
  - [ ] 创建数据预览表格（QTableWidget，前10行）
  - [ ] 实现列映射工具（QComboBox选择）
- [ ] 任务9：创建配置预览面板和参数摘要 (AC: 9)

  - [ ] 创建src/interfaces/desktop/widgets/config_preview_widget.py
  - [ ] 实现ConfigPreviewWidget配置预览面板
  - [ ] 添加实时参数摘要（QTextEdit显示配置摘要）
  - [ ] 创建配置变更历史跟踪
  - [ ] 实现配置导出和导入功能
  - [ ] 添加配置模板保存和加载
  - [ ] 创建配置比较和差异显示
- [ ] 任务10：确保专业科学研究界面标准 (AC: 10)

  - [ ] 应用ai-frontend-prompts.md的色彩方案（主色调#1A365D，辅助色#4A90E2）
  - [ ] 实现科学研究级别的专业桌面界面美学
  - [ ] 添加渐进式披露的复杂参数配置
  - [ ] 确保透明度至上（清晰展示模型假设、计算过程）
  - [ ] 实现可重现性支持（便于保存、记录和重复配置）
  - [ ] 添加专业字体配置（系统默认字体，等宽字体用于数据显示）

## Dev Notes

### 前一个故事的关键见解

从故事1.5的实现中学到：

- ✅ PyQt6桌面应用框架已成功建立，可以在此基础上扩展
- ✅ 基本的人群配置界面已实现，可以作为第一步的基础
- ✅ 表单验证系统已建立，可以扩展用于复杂参数验证
- ⚠️ 需要实现更复杂的向导式界面和专业级数据可视化

### 架构上下文

#### 技术栈要求 [Source: architecture/tech-stack.md]

- **桌面框架**: PyQt6 6.5+ - 跨平台桌面应用，原生性能，丰富的UI组件
- **数据可视化**: Matplotlib 3.7+ - 科学图表绘制，出版级图表质量
- **交互式图表**: Plotly 5.15+ - 交互式数据可视化，现代化交互体验
- **数据处理**: Pandas 2.0+ - 数据操作和分析，Excel/CSV集成
- **配置管理**: PyYAML 6.0+ - 配置文件处理，人类可读的配置格式

#### 前端架构规范 [Source: architecture/frontend-architecture.md]

- **应用程序结构**: src/interfaces/desktop/ 目录结构
- **窗口组件**: windows/ 目录用于主要窗口类
- **UI组件**: widgets/ 目录用于可重用组件
- **工具模块**: utils/ 目录用于验证器和辅助函数
- **资源文件**: resources/ 目录用于图标、样式、翻译

#### 项目结构对齐 [Source: architecture/unified-project-structure.md]

- **配置向导**: src/interfaces/desktop/windows/config_wizard.py
- **参数面板**: src/interfaces/desktop/widgets/parameter_panel.py
- **图表组件**: src/interfaces/desktop/widgets/chart_widget.py
- **验证器**: src/interfaces/desktop/utils/validators.py
- **样式资源**: src/interfaces/desktop/resources/styles/

#### 数据模型规范 [Source: architecture/data-models.md]

- **疾病状态**: DiseaseState枚举（normal, low_risk_adenoma, high_risk_adenoma等）
- **通路类型**: PathwayType（腺瘤-癌变85%，锯齿状腺瘤15%）
- **风险因素**: RiskFactorType枚举（家族史、IBD、肥胖、糖尿病、吸烟、久坐）
- **筛查工具**: ScreeningTool类（FIT、结肠镜、乙状结肠镜）
- **经济参数**: 成本计算器、QALY计算器

### 技术实现要点

#### 6步向导架构

```python
class AdvancedConfigWizard(QDialog):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("高级模拟配置向导")
        self.setMinimumSize(1000, 700)
      
        # 创建步骤页面
        self.pages = [
            PopulationConfigPage(),      # 步骤1：人群设置
            DiseaseModelingPage(),       # 步骤2：疾病建模
            ScreeningStrategyPage(),     # 步骤3：筛查策略
            EconomicParametersPage(),    # 步骤4：经济参数
            CalibrationSettingsPage(),   # 步骤5：校准设置
            SimulationExecutionPage()    # 步骤6：模拟执行
        ]
```

#### 实时验证系统

- QValidator进行表单验证
- 参数冲突检测和警告
- 实时参数影响预览
- 智能默认值建议

#### 数据导入集成

- 拖拽事件处理（QDrag和QMimeData）
- 多格式文件支持（CSV、Excel、JSON）
- 数据预览和列映射
- 格式验证和错误处理

### 项目结构注意事项

- 所有新文件应放置在src/interfaces/desktop/目录结构中
- 遵循现有的命名约定和模块组织
- 确保与现有的Application和MainWindow类集成
- 保持与故事1.5实现的兼容性

## Testing

### 测试文件位置

- `tests/unit/test_advanced_config_wizard.py`
- `tests/unit/test_disease_modeling_widget.py`
- `tests/unit/test_screening_strategy_widget.py`
- `tests/integration/test_config_wizard_integration.py`

### 测试标准

- 6步向导导航和状态管理测试
- 参数验证和错误处理测试
- 数据导入和格式验证测试
- 配置保存和加载功能测试
- 用户界面响应性和可用性测试

### 特定测试要求

- 向导步骤切换响应时间 < 100ms
- 大数据文件导入处理 < 5秒
- 参数验证实时反馈 < 50ms
- 配置保存和加载 < 1秒

## Change Log

| Date       | Version | Description                                  | Author       |
| ---------- | ------- | -------------------------------------------- | ------------ |
| 2025-01-31 | 1.0     | 初始故事创建，基于ai-frontend-prompts.md要求 | Scrum Master |
